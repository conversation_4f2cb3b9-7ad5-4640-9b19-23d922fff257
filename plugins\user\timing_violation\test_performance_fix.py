#!/usr/bin/env python3
"""
测试数据库合并对话框性能优化效果

验证异步加载数据库统计信息是否解决了GUI卡顿问题。
"""

import os
import sys
import time
import tempfile
import shutil
from datetime import datetime

# 添加插件路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
    from PyQt5.QtCore import QTimer
    from models import ViolationDataModel
    from main_window import DatabaseMergeDialog
    PYQT_AVAILABLE = True
except ImportError:
    print("PyQt5不可用，无法进行GUI性能测试")
    PYQT_AVAILABLE = False
    sys.exit(1)


def create_large_test_database(db_path: str, violation_count: int = 1000):
    """创建包含大量数据的测试数据库
    
    Args:
        db_path: 数据库路径
        violation_count: 违例数量
    """
    # 确保目录存在
    os.makedirs(os.path.dirname(db_path), exist_ok=True)
    
    # 临时切换工作目录
    original_cwd = os.getcwd()
    os.chdir(os.path.dirname(db_path))
    
    try:
        # 创建数据模型
        data_model = ViolationDataModel()
        
        print(f"正在创建包含 {violation_count} 个违例的测试数据库...")
        
        # 批量创建违例数据
        batch_size = 100
        for batch_start in range(0, violation_count, batch_size):
            batch_end = min(batch_start + batch_size, violation_count)
            violations_data = []
            
            for i in range(batch_start, batch_end):
                case_num = i // 10 + 1
                violation_num = i % 10 + 1
                
                violation = {
                    'NUM': violation_num,
                    'Hier': f'tb_top.module_{i % 50}.submodule_{i % 20}',
                    'Time': f'{1000 + i*10} FS',
                    'time_fs': (1000 + i*10) * 1000,
                    'Check': f'setup(posedge clk_{i % 5}, data_{i % 3})'
                }
                violations_data.append(violation)
            
            # 添加批次数据
            added_count = data_model.add_violations(
                violations_data,
                f"large_test_case_{case_num}",
                "npg_f1_ssg",
                f"/path/to/large_test/vio_summary_{case_num}.log"
            )
            
            if batch_start % 500 == 0:
                print(f"  已创建 {batch_end} / {violation_count} 个违例...")
        
        # 添加一些确认记录和模式
        violations = data_model.get_violations_by_case("large_test_case_1", "npg_f1_ssg")
        if violations:
            # 确认前几个违例
            for i, violation in enumerate(violations[:10]):
                data_model.update_confirmation(
                    violation['id'],
                    'confirmed',
                    f'tester_{i % 3}',
                    '通过',
                    f'测试确认 #{i}'
                )
            
            # 添加一些模式
            for i in range(5):
                data_model.save_pattern(
                    f'tb_top.module_{i}.*',
                    f'setup*clk_{i}*',
                    f'tester_{i}',
                    '通过',
                    f'测试模式 #{i}'
                )
        
        print(f"✓ 大型测试数据库创建完成: {db_path}")
        return True
        
    except Exception as e:
        print(f"✗ 创建大型测试数据库失败: {str(e)}")
        return False
    finally:
        os.chdir(original_cwd)


class PerformanceTestWindow(QMainWindow):
    """性能测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("数据库合并对话框性能测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建测试数据库
        self.setup_test_databases()
        
        # 创建界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 测试按钮
        self.test_btn = QPushButton("测试数据库合并对话框性能")
        self.test_btn.clicked.connect(self.test_dialog_performance)
        layout.addWidget(self.test_btn)
        
        # 结果显示
        from PyQt5.QtWidgets import QTextEdit
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        self.log("性能测试窗口已准备就绪")
        self.log("点击按钮测试数据库合并对话框的打开速度")
    
    def setup_test_databases(self):
        """设置测试数据库"""
        self.temp_dir = tempfile.mkdtemp(prefix="timing_violation_perf_test_")
        self.log(f"测试目录: {self.temp_dir}")
        
        # 创建主数据库（大型）
        self.main_db_path = os.path.join(self.temp_dir, "main", "VIOLATION_CHECK", "timing_violations.db")
        create_large_test_database(self.main_db_path, 1000)
        
        # 创建几个较小的源数据库
        self.source_db_paths = []
        for i in range(3):
            source_db_path = os.path.join(self.temp_dir, f"source_{i}", "VIOLATION_CHECK", "timing_violations.db")
            create_large_test_database(source_db_path, 200)
            self.source_db_paths.append(source_db_path)
        
        # 切换到主数据库目录
        self.original_cwd = os.getcwd()
        os.chdir(os.path.dirname(self.main_db_path))
        
        # 创建数据模型
        self.data_model = ViolationDataModel()
        
        self.log("测试数据库创建完成")
    
    def test_dialog_performance(self):
        """测试对话框性能"""
        self.log("\n" + "="*50)
        self.log("开始性能测试...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 创建对话框
        self.log("正在创建数据库合并对话框...")
        dialog = DatabaseMergeDialog(self.data_model, self)
        
        # 记录对话框创建完成时间
        creation_time = time.time()
        creation_duration = (creation_time - start_time) * 1000
        
        self.log(f"✓ 对话框创建完成，耗时: {creation_duration:.2f} ms")
        
        # 预加载一些源数据库
        dialog.selected_databases = self.source_db_paths[:2]
        
        # 记录列表更新开始时间
        list_start_time = time.time()
        dialog.update_database_list()
        list_update_time = time.time()
        list_duration = (list_update_time - list_start_time) * 1000
        
        self.log(f"✓ 数据库列表更新启动，耗时: {list_duration:.2f} ms")
        
        # 显示对话框
        show_start_time = time.time()
        
        # 使用定时器来测量对话框完全显示的时间
        def check_dialog_ready():
            show_duration = (time.time() - show_start_time) * 1000
            self.log(f"✓ 对话框显示完成，总耗时: {show_duration:.2f} ms")
            
            total_duration = (time.time() - start_time) * 1000
            self.log(f"\n总体性能评估:")
            self.log(f"  对话框创建: {creation_duration:.2f} ms")
            self.log(f"  列表更新启动: {list_duration:.2f} ms")
            self.log(f"  对话框显示: {show_duration:.2f} ms")
            self.log(f"  总耗时: {total_duration:.2f} ms")
            
            if total_duration < 100:
                self.log("✓ 性能优秀 (< 100ms)")
            elif total_duration < 300:
                self.log("✓ 性能良好 (< 300ms)")
            elif total_duration < 500:
                self.log("⚠ 性能一般 (< 500ms)")
            else:
                self.log("✗ 性能较差 (>= 500ms)")
            
            self.log("\n对话框已打开，可以进行交互测试")
            self.log("关闭对话框以继续...")
        
        # 延迟检查，确保对话框完全渲染
        QTimer.singleShot(50, check_dialog_ready)
        
        # 显示对话框
        dialog.show()
        
        # 等待对话框关闭
        result = dialog.exec_()
        
        self.log(f"\n对话框已关闭，返回值: {result}")
        self.log("性能测试完成")
    
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_message = f"[{timestamp}] {message}"
        print(log_message)
        if hasattr(self, 'result_text'):
            self.result_text.append(log_message)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 恢复工作目录
        os.chdir(self.original_cwd)
        
        # 清理测试文件
        try:
            shutil.rmtree(self.temp_dir)
            self.log(f"测试文件已清理: {self.temp_dir}")
        except Exception as e:
            self.log(f"清理测试文件失败: {str(e)}")
        
        super().closeEvent(event)


def main():
    """主函数"""
    if not PYQT_AVAILABLE:
        print("PyQt5不可用，无法进行性能测试")
        return
    
    print("数据库合并对话框性能测试")
    print("=" * 50)
    print("此测试将:")
    print("1. 创建包含大量数据的测试数据库")
    print("2. 测量数据库合并对话框的打开速度")
    print("3. 验证异步加载是否解决了GUI卡顿问题")
    print()
    
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_window = PerformanceTestWindow()
    test_window.show()
    
    # 运行应用
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
